import mongoose, { Document, Schema } from 'mongoose';

export interface ISubscription extends Document {
  _id: string;
  gymId: string;
  memberId: string;
  packageId: string;
  subscriptionNumber: string;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'expired' | 'cancelled' | 'suspended' | 'pending';
  payment: {
    amount: number;
    currency: string;
    setupFee?: number;
    totalPaid: number;
    paymentMethod: 'cash' | 'card' | 'bank_transfer' | 'online' | 'other';
    paymentDate: Date;
    transactionId?: string;
    discount?: number;
    discountReason?: string;
    originalAmount?: number; // Store original package amount for reference
  };
  autoRenewal: {
    enabled: boolean;
    renewalDate?: Date;
    notificationSent: boolean;
  };
  usage: {
    classesAttended: number;
    trainerSessions: number;
    lastActivity?: Date;
  };
  notes?: string;
  customTerms?: string; // Allow custom terms and conditions
  createdBy: string; // User ID who created the subscription
  createdAt: Date;
  updatedAt: Date;
}

const SubscriptionSchema = new Schema<ISubscription>({
  gymId: {
    type: String,
    required: true,
    index: true,
  },
  memberId: {
    type: String,
    required: true,
    index: true,
  },
  packageId: {
    type: String,
    required: true,
    index: true,
  },
  subscriptionNumber: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
  },
  startDate: {
    type: Date,
    required: true,
  },
  endDate: {
    type: Date,
    required: true,
  },
  status: {
    type: String,
    enum: ['active', 'expired', 'cancelled', 'suspended', 'pending'],
    default: 'active',
  },
  payment: {
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    currency: {
      type: String,
      required: true,
      default: 'USD',
      uppercase: true,
    },
    setupFee: {
      type: Number,
      min: 0,
      default: 0,
    },
    totalPaid: {
      type: Number,
      required: true,
      min: 0,
    },
    paymentMethod: {
      type: String,
      enum: ['cash', 'card', 'bank_transfer', 'online', 'other'],
      required: true,
    },
    paymentDate: {
      type: Date,
      required: true,
    },
    transactionId: String,
    discount: {
      type: Number,
      min: 0,
      default: 0,
    },
    discountReason: {
      type: String,
      maxlength: 200,
    },
    originalAmount: {
      type: Number,
      min: 0,
    },
  },
  autoRenewal: {
    enabled: {
      type: Boolean,
      default: false,
    },
    renewalDate: Date,
    notificationSent: {
      type: Boolean,
      default: false,
    },
  },
  usage: {
    classesAttended: {
      type: Number,
      default: 0,
    },
    trainerSessions: {
      type: Number,
      default: 0,
    },
    lastActivity: Date,
  },
  notes: {
    type: String,
    maxlength: 1000,
  },
  customTerms: {
    type: String,
    maxlength: 1000,
  },
  createdBy: {
    type: String,
    required: true,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
SubscriptionSchema.index({ gymId: 1, status: 1 });
SubscriptionSchema.index({ gymId: 1, memberId: 1 });
SubscriptionSchema.index({ gymId: 1, endDate: 1 });
SubscriptionSchema.index({ subscriptionNumber: 1 });

// Virtual for days remaining
SubscriptionSchema.virtual('daysRemaining').get(function() {
  const today = new Date();
  const endDate = new Date(this.endDate);
  const diffTime = endDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
});

// Virtual for is expired
SubscriptionSchema.virtual('isExpired').get(function() {
  return new Date() > new Date(this.endDate);
});

// Virtual for is expiring soon (within 7 days)
SubscriptionSchema.virtual('isExpiringSoon').get(function() {
  const daysRemaining = this.daysRemaining;
  return daysRemaining > 0 && daysRemaining <= 7;
});

// Virtual for subscription duration
SubscriptionSchema.virtual('duration').get(function() {
  const startDate = new Date(this.startDate);
  const endDate = new Date(this.endDate);
  const diffTime = endDate.getTime() - startDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for formatted payment amount
SubscriptionSchema.virtual('formattedAmount').get(function() {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: this.payment.currency,
  }).format(this.payment.amount);
});

// Pre-save middleware to generate subscription number
SubscriptionSchema.pre('save', async function(next) {
  if (this.isNew && !this.subscriptionNumber) {
    const count = await mongoose.model('Subscription').countDocuments({ gymId: this.gymId });
    const year = new Date().getFullYear();
    this.subscriptionNumber = `${this.gymId.toUpperCase()}-SUB-${year}-${String(count + 1).padStart(4, '0')}`;
  }
  
  // Auto-update status based on dates
  if (this.isModified('endDate') || this.isModified('startDate')) {
    const now = new Date();
    const startDate = new Date(this.startDate);
    const endDate = new Date(this.endDate);
    
    if (now < startDate) {
      this.status = 'pending';
    } else if (now > endDate && this.status === 'active') {
      this.status = 'expired';
    }
  }
  
  next();
});

// Static method to get subscriptions by gym
SubscriptionSchema.statics.getByGym = function(gymId: string, status?: string) {
  const query: any = { gymId };
  if (status) {
    query.status = status;
  }
  return this.find(query)
    .populate('memberId', 'personalInfo membershipId')
    .populate('packageId', 'name type pricing')
    .sort({ createdAt: -1 });
};

// Static method to get active subscriptions for a member
SubscriptionSchema.statics.getActiveMemberSubscriptions = function(gymId: string, memberId: string) {
  return this.find({
    gymId,
    memberId,
    status: 'active',
    startDate: { $lte: new Date() },
    endDate: { $gte: new Date() },
  }).populate('packageId', 'name type pricing restrictions');
};

// Static method to get expiring subscriptions
SubscriptionSchema.statics.getExpiringSubscriptions = function(gymId: string, days: number = 7) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  
  return this.find({
    gymId,
    status: 'active',
    endDate: {
      $gte: new Date(),
      $lte: futureDate,
    },
  }).populate('memberId', 'personalInfo preferences');
};

// Instance method to extend subscription
SubscriptionSchema.methods.extend = function(days: number) {
  const newEndDate = new Date(this.endDate);
  newEndDate.setDate(newEndDate.getDate() + days);
  this.endDate = newEndDate;
  
  if (this.status === 'expired') {
    this.status = 'active';
  }
  
  return this.save();
};

// Instance method to cancel subscription
SubscriptionSchema.methods.cancel = function(reason?: string) {
  this.status = 'cancelled';
  if (reason) {
    this.notes = (this.notes || '') + `\nCancelled: ${reason}`;
  }
  return this.save();
};

export default mongoose.models.Subscription || mongoose.model<ISubscription>('Subscription', SubscriptionSchema);
