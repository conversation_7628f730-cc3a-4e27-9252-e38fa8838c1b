'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Building2, 
  Users, 
  Plus, 
  Settings, 
  BarChart3, 
  Shield,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { UserRole } from '@/types/user';

interface DashboardStats {
  totalGyms: number;
  activeGyms: number;
  totalUsers: number;
  totalMembers: number;
}

export default function SuperAdminDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats>({
    totalGyms: 0,
    activeGyms: 0,
    totalUsers: 0,
    totalMembers: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Check authentication and authorization
  useEffect(() => {
    if (status === 'loading') return;
    
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session?.user?.role !== UserRole.SUPER_ADMIN) {
      router.push('/dashboard');
      return;
    }

    // Load dashboard stats
    loadDashboardStats();
  }, [status, session, router]);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API calls
      // For now, using mock data
      setStats({
        totalGyms: 12,
        activeGyms: 10,
        totalUsers: 245,
        totalMembers: 1850
      });
    } catch (err) {
      setError('Failed to load dashboard statistics');
      console.error('Dashboard stats error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGym = () => {
    router.push('/admin/gyms/create');
  };

  const handleManageGyms = () => {
    router.push('/admin/gyms');
  };

  const handleManageUsers = () => {
    router.push('/admin/users');
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-600 mb-4">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Error</span>
            </div>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={loadDashboardStats} className="w-full">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-red-600 mr-3" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Super Admin Dashboard
                </h1>
                <p className="text-sm text-gray-500">GymD Platform Management</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {session?.user?.firstName}
              </span>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => router.push('/dashboard')}
              >
                Regular Dashboard
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Platform Overview
          </h2>
          <p className="text-gray-600">
            Manage gyms, users, and platform-wide settings.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Gyms</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalGyms}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeGyms} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Gyms</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeGyms}</div>
              <p className="text-xs text-muted-foreground">
                {Math.round((stats.activeGyms / stats.totalGyms) * 100)}% of total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalUsers}</div>
              <p className="text-xs text-muted-foreground">
                All platform users
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalMembers}</div>
              <p className="text-xs text-muted-foreground">
                Across all gyms
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building2 className="h-5 w-5 mr-2 text-blue-600" />
                Gym Management
              </CardTitle>
              <CardDescription>
                Create and manage gym accounts on the platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={handleCreateGym}
                className="w-full"
                variant="gradient"
                size="lg"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Gym
              </Button>
              <Button 
                onClick={handleManageGyms}
                variant="outline"
                className="w-full"
                size="lg"
              >
                <Settings className="h-4 w-4 mr-2" />
                Manage Existing Gyms
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2 text-green-600" />
                User Management
              </CardTitle>
              <CardDescription>
                Manage platform users and permissions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={handleManageUsers}
                variant="outline"
                className="w-full"
                size="lg"
              >
                <Users className="h-4 w-4 mr-2" />
                Manage Users
              </Button>
              <Button 
                variant="outline"
                className="w-full"
                size="lg"
                disabled
              >
                <Shield className="h-4 w-4 mr-2" />
                Platform Settings
                <span className="ml-2 text-xs bg-gray-200 px-2 py-1 rounded">Soon</span>
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
