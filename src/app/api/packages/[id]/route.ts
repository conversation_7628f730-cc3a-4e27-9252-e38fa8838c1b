import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Package from '@/models/Package';
import { packageSchema, idParamSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';


interface RouteContext {
  params: Promise<{ id: string }>;
}

/**
 * GET /api/packages/[id] - Get package by ID
 */
export const GET = withErrorHandling(async (request: NextRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // Get gymId from query params (temporary solution)
  const { searchParams } = new URL(request.url);
  const gymId = searchParams.get('gymId');
  if (!gymId) {
    return createErrorResponse('gymId parameter is required', 400);
  }

  const packageData = await Package.findOne({
    _id: validatedParams.id,
    gymId: gymId
  }).select('-__v').lean();
  
  if (!packageData) {
    return createErrorResponse('Package not found', 404);
  }
  
  return createSuccessResponse(packageData, 'Package retrieved successfully');
});

/**
 * PUT /api/packages/[id] - Update package by ID
 */
export const PUT = withErrorHandling(async (request: NextRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // Get gymId from query params (temporary solution)
  const { searchParams } = new URL(request.url);
  const gymId = searchParams.get('gymId');
  if (!gymId) {
    return createErrorResponse('gymId parameter is required', 400);
  }

  const body = await request.json();

  // For partial updates, make all fields optional except gymId
  const updateSchema = packageSchema.partial().omit({ gymId: true });
  const validatedData = updateSchema.parse(body);

  // Update package
  const updatedPackage = await Package.findOneAndUpdate(
    { _id: validatedParams.id, gymId: gymId },
    {
      ...validatedData,
      updatedAt: new Date(),
    },
    { 
      new: true, 
      runValidators: true,
      select: '-__v'
    }
  ).lean();
  
  if (!updatedPackage) {
    return createErrorResponse('Package not found', 404);
  }
  
  return createSuccessResponse(updatedPackage, 'Package updated successfully');
});

/**
 * DELETE /api/packages/[id] - Delete package by ID
 */
export const DELETE = withErrorHandling(async (request: NextRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // Get gymId from query params (temporary solution)
  const { searchParams } = new URL(request.url);
  const gymId = searchParams.get('gymId');
  if (!gymId) {
    return createErrorResponse('gymId parameter is required', 400);
  }

  const deletedPackage = await Package.findOneAndDelete({
    _id: validatedParams.id,
    gymId: gymId
  }).select('-__v').lean();
  
  if (!deletedPackage) {
    return createErrorResponse('Package not found', 404);
  }
  
  return createSuccessResponse(deletedPackage, 'Package deleted successfully');
});
