import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Package from '@/models/Package';
import { packageSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createPaginatedResponse,
  withErrorHandling,
  extractPaginationParams,
  buildSortObject,
  buildSearchFilter
} from '@/lib/utils/api';


/**
 * GET /api/packages - List packages for current gym
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const { searchParams } = new URL(request.url);
  const { page, limit, skip, search, sortBy, sortOrder } = extractPaginationParams(searchParams);

  // Get gymId from query params (temporary solution)
  const gymId = searchParams.get('gymId');
  if (!gymId) {
    return createErrorResponse('gymId parameter is required', 400);
  }

  // Create filter for gym isolation
  const tenantFilter = { gymId };
  
  // Build search filter
  const searchFilter = buildSearchFilter(search, ['name', 'description', 'type']);
  
  // Combine filters
  const filter = { ...tenantFilter, ...searchFilter };
  
  // Build sort object
  const sortObject = buildSortObject(sortBy, sortOrder);
  
  // Get total count for pagination
  const total = await Package.countDocuments(filter);
  
  // Get packages with pagination
  const packages = await Package.find(filter)
    .sort(sortObject)
    .skip(skip)
    .limit(limit)
    .select('-__v')
    .lean();
  
  return createPaginatedResponse(packages, page, limit, total, 'Packages retrieved successfully');
});

/**
 * POST /api/packages - Create a new package
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const body = await request.json();

  // Validate request body
  const validatedData = packageSchema.parse(body);

  // Create new package with gym isolation
  const packageData = new Package({
    ...validatedData,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  
  await packageData.save();
  
  return createSuccessResponse(
    packageData.toObject({ transform: (doc, ret) => { delete ret.__v; return ret; } }),
    'Package created successfully',
    201
  );
});
