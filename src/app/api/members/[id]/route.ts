import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Member from '@/models/Member';
import { memberSchema, idParamSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';
import { requireStaff } from '@/lib/auth/middleware';
import { AuthenticatedRequest } from '@/lib/auth/middleware';

interface RouteContext {
  params: Promise<{ id: string }>;
}

/**
 * GET /api/members/[id] - Get member by ID
 */
export const GET = requireStaff(withErrorHandling(async (request: AuthenticatedRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  const member = await Member.findOne({
    _id: validatedParams.id,
    gymId: gymId
  }).select('-__v').lean();
  
  if (!member) {
    return createErrorResponse('Member not found', 404);
  }
  
  return createSuccessResponse(member, 'Member retrieved successfully');
}));

/**
 * PUT /api/members/[id] - Update member by ID
 */
export const PUT = requireStaff(withErrorHandling(async (request: AuthenticatedRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  const body = await request.json();
  
  // Validate request body (partial update)
  const validatedData = memberSchema.partial().parse(body);
  
  // Check if email is being updated and if it already exists
  if (validatedData.personalInfo?.email) {
    const existingMember = await Member.findOne({ 
      gymId,
      'personalInfo.email': validatedData.personalInfo.email,
      _id: { $ne: validatedParams.id }
    });
    if (existingMember) {
      return createErrorResponse('Member with this email already exists in this gym', 409);
    }
  }

  const updatedMember = await Member.findOneAndUpdate(
    { _id: validatedParams.id, gymId: gymId },
    { ...validatedData, updatedAt: new Date() },
    { new: true, runValidators: true }
  ).select('-__v').lean();
  
  if (!updatedMember) {
    return createErrorResponse('Member not found', 404);
  }
  
  return createSuccessResponse(updatedMember, 'Member updated successfully');
}));

/**
 * DELETE /api/members/[id] - Delete member by ID
 */
export const DELETE = requireStaff(withErrorHandling(async (request: AuthenticatedRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  // Check if member has active subscriptions
  const Subscription = (await import('@/models/Subscription')).default;
  const activeSubscriptions = await Subscription.find({
    gymId,
    memberId: validatedParams.id,
    status: 'active'
  });

  if (activeSubscriptions.length > 0) {
    return createErrorResponse(
      'Cannot delete member with active subscriptions. Please cancel or transfer subscriptions first.',
      409
    );
  }

  const deletedMember = await Member.findOneAndDelete({
    _id: validatedParams.id,
    gymId: gymId
  }).select('-__v').lean();
  
  if (!deletedMember) {
    return createErrorResponse('Member not found', 404);
  }
  
  return createSuccessResponse(
    { id: deletedMember._id },
    'Member deleted successfully'
  );
}));
