import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Subscription from '@/models/Subscription';
import Member from '@/models/Member';
import Package from '@/models/Package';
import { subscriptionSchema, idParamSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createPaginatedResponse,
  withErrorHandling,
  extractPaginationParams,
  buildSortObject
} from '@/lib/utils/api';
import { requireStaff } from '@/lib/auth/middleware';
import { AuthenticatedRequest } from '@/lib/auth/middleware';

interface RouteContext {
  params: Promise<{ id: string }>;
}

/**
 * GET /api/members/[id]/subscriptions - Get all subscriptions for a specific member
 */
export const GET = requireStaff(withErrorHandling(async (request: AuthenticatedRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id: memberId } = await context.params;
  const validatedParams = idParamSchema.parse({ id: memberId });
  
  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  // Verify member exists and belongs to this gym
  const member = await Member.findOne({
    _id: validatedParams.id,
    gymId: gymId
  });
  
  if (!member) {
    return createErrorResponse('Member not found', 404);
  }

  const { searchParams } = new URL(request.url);
  const { page, limit, skip, sortBy, sortOrder } = extractPaginationParams(searchParams);

  // Create filter for member's subscriptions
  const filter: any = { 
    gymId: gymId,
    memberId: validatedParams.id
  };
  
  // Add status filter if provided
  const status = searchParams.get('status');
  if (status) {
    filter.status = status;
  }
  
  // Build sort object (default to newest first)
  const sortObject = buildSortObject(sortBy || 'createdAt', sortOrder || 'desc');
  
  // Get total count for pagination
  const total = await Subscription.countDocuments(filter);
  
  // Get subscriptions with pagination
  const subscriptions = await Subscription.find(filter)
    .sort(sortObject)
    .skip(skip)
    .limit(limit)
    .populate('packageId', 'name type pricing duration features benefits')
    .select('-__v')
    .lean();
  
  return createPaginatedResponse(
    subscriptions, 
    page, 
    limit, 
    total, 
    `Subscriptions for member ${member.personalInfo.firstName} ${member.personalInfo.lastName} retrieved successfully`
  );
}));

/**
 * POST /api/members/[id]/subscriptions - Create a new subscription for a specific member
 */
export const POST = requireStaff(withErrorHandling(async (request: AuthenticatedRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id: memberId } = await context.params;
  const validatedParams = idParamSchema.parse({ id: memberId });
  
  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  // Get createdBy from authenticated user
  const createdBy = request.user?.id;
  if (!createdBy) {
    return createErrorResponse('User context not found', 400);
  }

  // Verify member exists and belongs to this gym
  const member = await Member.findOne({
    _id: validatedParams.id,
    gymId: gymId
  });
  
  if (!member) {
    return createErrorResponse('Member not found', 404);
  }

  const body = await request.json();
  
  // Add memberId to the request body
  const subscriptionData = {
    ...body,
    memberId: validatedParams.id
  };

  // Validate request body
  const validatedData = subscriptionSchema.parse(subscriptionData);

  // Verify package exists and belongs to this gym
  const packageData = await Package.findOne({
    _id: validatedData.packageId,
    gymId: gymId,
    isActive: true
  });
  
  if (!packageData) {
    return createErrorResponse('Package not found or inactive in this gym', 404);
  }

  // Calculate end date - use custom end date if provided, otherwise calculate from package duration
  const startDate = new Date(validatedData.startDate);
  let endDate: Date;
  
  if (validatedData.endDate) {
    endDate = new Date(validatedData.endDate);
  } else {
    endDate = new Date(startDate);
    switch (packageData.duration.unit) {
      case 'days':
        endDate.setDate(endDate.getDate() + packageData.duration.value);
        break;
      case 'weeks':
        endDate.setDate(endDate.getDate() + (packageData.duration.value * 7));
        break;
      case 'months':
        endDate.setMonth(endDate.getMonth() + packageData.duration.value);
        break;
      case 'years':
        endDate.setFullYear(endDate.getFullYear() + packageData.duration.value);
        break;
    }
  }

  // Handle flexible pricing
  const originalAmount = packageData.pricing.amount;
  const customAmount = validatedData.payment.amount;
  const amount = customAmount !== undefined ? customAmount : originalAmount;
  const setupFee = validatedData.payment.setupFee || packageData.pricing.setupFee || 0;
  const discount = validatedData.payment.discount || 0;
  const finalAmount = Math.max(0, amount - discount);
  const totalPaid = finalAmount + setupFee;

  // Create new subscription
  const subscription = new Subscription({
    gymId,
    memberId: validatedParams.id,
    packageId: validatedData.packageId,
    startDate: startDate,
    endDate: endDate,
    status: startDate <= new Date() ? 'active' : 'pending',
    payment: {
      amount: finalAmount,
      currency: validatedData.payment.currency || packageData.pricing.currency,
      setupFee: setupFee,
      totalPaid: totalPaid,
      paymentMethod: validatedData.payment.paymentMethod,
      paymentDate: new Date(),
      transactionId: validatedData.payment.transactionId,
      discount: discount,
      discountReason: validatedData.payment.discountReason,
      originalAmount: originalAmount,
    },
    autoRenewal: validatedData.autoRenewal || { enabled: false },
    usage: {
      classesAttended: 0,
      trainerSessions: 0,
    },
    notes: validatedData.notes,
    customTerms: validatedData.customTerms,
    createdBy: createdBy,
  });
  
  await subscription.save();

  // Populate the created subscription for response
  const populatedSubscription = await Subscription.findById(subscription._id)
    .populate('packageId', 'name type pricing duration features benefits')
    .select('-__v')
    .lean();
  
  return createSuccessResponse(
    populatedSubscription,
    `Subscription created successfully for ${member.personalInfo.firstName} ${member.personalInfo.lastName}`,
    201
  );
}));
