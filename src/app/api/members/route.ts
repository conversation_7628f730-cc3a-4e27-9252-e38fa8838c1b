import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Member from '@/models/Member';
import { memberSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createPaginatedResponse,
  withErrorHandling,
  extractPaginationParams,
  buildSortObject,
  buildSearchFilter
} from '@/lib/utils/api';
import { requireStaff } from '@/lib/auth/middleware';
import { AuthenticatedRequest } from '@/lib/auth/middleware';

/**
 * GET /api/members - List members for current gym
 */
export const GET = requireStaff(withErrorHandling(async (request: AuthenticatedRequest) => {
  await connectToDatabase();
  
  const { searchParams } = new URL(request.url);
  const { page, limit, skip, search, sortBy, sortOrder } = extractPaginationParams(searchParams);

  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  // Create filter for gym isolation
  const tenantFilter = { gymId };
  
  // Build search filter for member-specific fields
  const searchFilter = buildSearchFilter(search, [
    'personalInfo.firstName', 
    'personalInfo.lastName', 
    'personalInfo.email',
    'personalInfo.phone',
    'membershipId'
  ]);
  
  // Combine filters
  const filter = { ...tenantFilter, ...searchFilter };
  
  // Add status filter if provided
  const status = searchParams.get('status');
  if (status) {
    filter.status = status;
  }
  
  // Build sort object
  const sortObject = buildSortObject(sortBy, sortOrder);
  
  // Get total count for pagination
  const total = await Member.countDocuments(filter);
  
  // Get members with pagination
  const members = await Member.find(filter)
    .sort(sortObject)
    .skip(skip)
    .limit(limit)
    .select('-__v')
    .lean();
  
  return createPaginatedResponse(members, page, limit, total, 'Members retrieved successfully');
}));

/**
 * POST /api/members - Create a new member
 */
export const POST = requireStaff(withErrorHandling(async (request: AuthenticatedRequest) => {
  await connectToDatabase();
  
  const body = await request.json();

  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  // Validate request body
  const validatedData = memberSchema.parse(body);

  // Check if member with same email already exists in this gym
  const existingMember = await Member.findOne({
    gymId,
    'personalInfo.email': validatedData.personalInfo.email
  });
  
  if (existingMember) {
    return createErrorResponse('Member with this email already exists in this gym', 409);
  }

  // Create new member with gym isolation
  const member = new Member({
    ...validatedData,
    gymId,
    status: 'active',
    joinDate: new Date(),
    totalVisits: 0,
  });
  
  await member.save();
  
  return createSuccessResponse(
    member.toObject({ transform: (doc, ret) => { delete ret.__v; return ret; } }),
    'Member created successfully',
    201
  );
}));
