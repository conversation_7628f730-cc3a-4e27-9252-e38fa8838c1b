import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Gym from '@/models/Gym';
import { gymSchema, idParamSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';

interface RouteContext {
  params: Promise<{ id: string }>;
}

/**
 * GET /api/gyms/[id] - Get gym by ID
 */
export const GET = withErrorHandling(async (request: NextRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // TODO: Add proper authorization check
  const query = { _id: validatedParams.id };
  
  const gym = await Gym.findOne(query).select('-__v').lean();
  
  if (!gym) {
    return createErrorResponse('Gym not found', 404);
  }
  
  return createSuccessResponse(gym, 'Gym retrieved successfully');
});

/**
 * PUT /api/gyms/[id] - Update gym by ID
 */
export const PUT = withErrorHandling(async (request: NextRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // TODO: Add proper authorization check
  
  const body = await request.json();
  
  // Validate request body
  const validatedData = gymSchema.partial().parse(body);
  
  // Check if subdomain is being updated and if it already exists
  if (validatedData.subdomain) {
    const existingGym = await Gym.findOne({ 
      subdomain: validatedData.subdomain,
      _id: { $ne: validatedParams.id }
    });
    if (existingGym) {
      return createErrorResponse('Subdomain already exists', 409);
    }
  }
  
  const updatedGym = await Gym.findByIdAndUpdate(
    validatedParams.id,
    { ...validatedData, updatedAt: new Date() },
    { new: true, runValidators: true }
  ).select('-__v').lean();
  
  if (!updatedGym) {
    return createErrorResponse('Gym not found', 404);
  }
  
  return createSuccessResponse(updatedGym, 'Gym updated successfully');
});

/**
 * DELETE /api/gyms/[id] - Delete gym by ID
 */
export const DELETE = withErrorHandling(async (request: NextRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // TODO: Add proper authorization check for gym deletion
  
  const deletedGym = await Gym.findByIdAndDelete(validatedParams.id);
  
  if (!deletedGym) {
    return createErrorResponse('Gym not found', 404);
  }
  
  return createSuccessResponse(
    { id: deletedGym._id },
    'Gym deleted successfully'
  );
});
