import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Gym from '@/models/Gym';
import User, { UserRole, UserStatus } from '@/models/User';
import { gymSchema, gymRegistrationSchema, paginationSchema } from '@/lib/validations/schemas';
import {
  createSuccessResponse,
  createErrorResponse,
  createPaginatedResponse,
  withErrorHandling,
  extractPaginationParams,
  buildSortObject,
  buildSearchFilter
} from '@/lib/utils/api';
import mongoose from 'mongoose';


/**
 * GET /api/gyms - List all gyms (Super Admin only) or current gym info
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();

  const { searchParams } = new URL(request.url);
  
  // If tenant is default (super admin), list all gyms with pagination
  if (tenant?.id === 'default') {
    const { page, limit, skip, search, sortBy, sortOrder } = extractPaginationParams(searchParams);
    
    // Build search filter
    const searchFilter = buildSearchFilter(search, ['name', 'subdomain', 'description']);
    
    // Build sort object
    const sortObject = buildSortObject(sortBy, sortOrder);
    
    // Get total count for pagination
    const total = await Gym.countDocuments(searchFilter);
    
    // Get gyms with pagination
    const gyms = await Gym.find(searchFilter)
      .sort(sortObject)
      .skip(skip)
      .limit(limit)
      .select('-__v')
      .lean();
    
    return createPaginatedResponse(gyms, page, limit, total, 'Gyms retrieved successfully');
  }
  
  // For specific tenant, return current gym info
  if (!tenant) {
    return createErrorResponse('Tenant not found', 404);
  }
  
  const gym = await Gym.findOne({ _id: tenant.id }).select('-__v').lean();
  
  if (!gym) {
    return createErrorResponse('Gym not found', 404);
  }
  
  return createSuccessResponse(gym, 'Gym information retrieved successfully');
});

/**
 * POST /api/gyms - Create a new gym with admin account
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();

  const body = await request.json();

  // Check if this is a gym registration with admin (new format) or just gym creation (old format)
  const isGymRegistration = body.gym && body.admin;

  if (isGymRegistration) {
    // New gym registration with admin account creation
    const validatedData = gymRegistrationSchema.parse(body);

    // Start a transaction for atomic operation
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Check if subdomain already exists
      const existingGym = await Gym.findOne({ subdomain: validatedData.gym.subdomain }).session(session);
      if (existingGym) {
        await session.abortTransaction();
        return createErrorResponse('Subdomain already exists', 409);
      }

      // Check if admin email already exists
      const existingUser = await User.findOne({ email: validatedData.admin.email }).session(session);
      if (existingUser) {
        await session.abortTransaction();
        return createErrorResponse('User with this email already exists', 409);
      }

      // Create new gym
      const gym = new Gym({
        ...validatedData.gym,
        isActive: true,
      });

      await gym.save({ session });

      // Create admin user for the gym
      const adminUser = new User({
        email: validatedData.admin.email,
        password: validatedData.admin.password,
        firstName: validatedData.admin.firstName,
        lastName: validatedData.admin.lastName,
        phone: validatedData.admin.phone,
        role: validatedData.admin.role as UserRole,
        status: UserStatus.ACTIVE,
        gymId: gym._id.toString(),
        gymIds: [gym._id.toString()],
        emailVerified: new Date(), // Auto-verify for gym owners
      });

      await adminUser.save({ session });

      // Commit the transaction
      await session.commitTransaction();

      return createSuccessResponse(
        {
          gym: gym.toObject({ transform: (doc, ret) => { delete ret.__v; return ret; } }),
          admin: {
            id: adminUser._id,
            email: adminUser.email,
            firstName: adminUser.firstName,
            lastName: adminUser.lastName,
            role: adminUser.role,
          }
        },
        'Gym and admin account created successfully',
        201
      );

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }

  } else {
    // Legacy gym creation (for super admin use)
    const validatedData = gymSchema.parse(body);

    // Check if subdomain already exists
    const existingGym = await Gym.findOne({ subdomain: validatedData.subdomain });
    if (existingGym) {
      return createErrorResponse('Subdomain already exists', 409);
    }

    // Create new gym
    const gym = new Gym({
      ...validatedData,
      isActive: true,
    });

    await gym.save();

    return createSuccessResponse(
      gym.toObject({ transform: (doc, ret) => { delete ret.__v; return ret; } }),
      'Gym created successfully',
      201
    );
  }
});
