import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Subscription from '@/models/Subscription';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createPaginatedResponse,
  withErrorHandling,
  extractPaginationParams,
  buildSortObject
} from '@/lib/utils/api';
import { requireStaff } from '@/lib/auth/middleware';
import { AuthenticatedRequest } from '@/lib/auth/middleware';

/**
 * GET /api/subscriptions/expiring - Get subscriptions expiring within specified days
 */
export const GET = requireStaff(withErrorHandling(async (request: AuthenticatedRequest) => {
  await connectToDatabase();
  
  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  const { searchParams } = new URL(request.url);
  const { page, limit, skip, sortBy, sortOrder } = extractPaginationParams(searchParams);

  // Get days parameter (default to 7 days)
  const daysParam = searchParams.get('days');
  const days = daysParam ? parseInt(daysParam, 10) : 7;
  
  if (isNaN(days) || days < 0) {
    return createErrorResponse('Invalid days parameter. Must be a non-negative number.', 400);
  }

  // Calculate the date range
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Start of today
  
  const futureDate = new Date(today);
  futureDate.setDate(futureDate.getDate() + days);
  futureDate.setHours(23, 59, 59, 999); // End of the target day

  // Create filter for expiring subscriptions
  const filter: any = {
    gymId: gymId,
    status: 'active', // Only active subscriptions can expire
    endDate: {
      $gte: today,
      $lte: futureDate
    }
  };

  // Add member filter if provided
  const memberId = searchParams.get('memberId');
  if (memberId) {
    filter.memberId = memberId;
  }

  // Add package filter if provided
  const packageId = searchParams.get('packageId');
  if (packageId) {
    filter.packageId = packageId;
  }
  
  // Build sort object (default to expiring soonest first)
  const sortObject = buildSortObject(sortBy || 'endDate', sortOrder || 'asc');
  
  // Get total count for pagination
  const total = await Subscription.countDocuments(filter);
  
  // Get expiring subscriptions with pagination
  const subscriptions = await Subscription.find(filter)
    .sort(sortObject)
    .skip(skip)
    .limit(limit)
    .populate('memberId', 'personalInfo membershipId status')
    .populate('packageId', 'name type pricing duration')
    .select('-__v')
    .lean();

  // Add days remaining to each subscription
  const subscriptionsWithDaysRemaining = subscriptions.map(subscription => ({
    ...subscription,
    daysRemaining: Math.ceil((new Date(subscription.endDate).getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
  }));
  
  return createPaginatedResponse(
    subscriptionsWithDaysRemaining, 
    page, 
    limit, 
    total, 
    `Found ${total} subscription(s) expiring within ${days} day(s)`
  );
}));
