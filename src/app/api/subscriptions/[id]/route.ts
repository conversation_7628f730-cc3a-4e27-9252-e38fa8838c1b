import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Subscription from '@/models/Subscription';
import Package from '@/models/Package';
import { subscriptionSchema, idParamSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';
import { requireStaff } from '@/lib/auth/middleware';
import { AuthenticatedRequest } from '@/lib/auth/middleware';

interface RouteContext {
  params: Promise<{ id: string }>;
}

/**
 * GET /api/subscriptions/[id] - Get subscription by ID
 */
export const GET = requireStaff(withErrorHandling(async (request: AuthenticatedRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  const subscription = await Subscription.findOne({
    _id: validatedParams.id,
    gymId: gymId
  })
    .populate('memberId', 'personalInfo membershipId status')
    .populate('packageId', 'name type pricing duration features benefits restrictions')
    .select('-__v')
    .lean();
  
  if (!subscription) {
    return createErrorResponse('Subscription not found', 404);
  }
  
  return createSuccessResponse(subscription, 'Subscription retrieved successfully');
}));

/**
 * PUT /api/subscriptions/[id] - Update subscription by ID
 */
export const PUT = requireStaff(withErrorHandling(async (request: AuthenticatedRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  const body = await request.json();
  
  // For subscription updates, we allow partial updates with custom validation
  const allowedUpdates = [
    'startDate', 'endDate', 'status', 'payment', 'autoRenewal', 'notes'
  ];
  
  const updateData: any = {};
  
  // Handle custom start/end date updates
  if (body.startDate) {
    updateData.startDate = new Date(body.startDate);
  }
  
  if (body.endDate) {
    updateData.endDate = new Date(body.endDate);
  }
  
  // Handle status updates
  if (body.status && ['active', 'expired', 'cancelled', 'suspended', 'pending'].includes(body.status)) {
    updateData.status = body.status;
  }
  
  // Handle payment updates (allow price overrides)
  if (body.payment) {
    const currentSubscription = await Subscription.findOne({
      _id: validatedParams.id,
      gymId: gymId
    });
    
    if (!currentSubscription) {
      return createErrorResponse('Subscription not found', 404);
    }
    
    updateData.payment = {
      ...currentSubscription.payment,
      ...body.payment
    };
    
    // Recalculate totalPaid if amount or setupFee changed
    if (body.payment.amount !== undefined || body.payment.setupFee !== undefined) {
      const amount = body.payment.amount ?? currentSubscription.payment.amount;
      const setupFee = body.payment.setupFee ?? currentSubscription.payment.setupFee;
      updateData.payment.totalPaid = amount + setupFee;
    }
  }
  
  // Handle auto-renewal updates
  if (body.autoRenewal) {
    updateData.autoRenewal = body.autoRenewal;
  }
  
  // Handle notes updates
  if (body.notes !== undefined) {
    updateData.notes = body.notes;
  }
  
  // Add updatedAt timestamp
  updateData.updatedAt = new Date();

  const updatedSubscription = await Subscription.findOneAndUpdate(
    { _id: validatedParams.id, gymId: gymId },
    updateData,
    { new: true, runValidators: true }
  )
    .populate('memberId', 'personalInfo membershipId status')
    .populate('packageId', 'name type pricing duration')
    .select('-__v')
    .lean();
  
  if (!updatedSubscription) {
    return createErrorResponse('Subscription not found', 404);
  }
  
  return createSuccessResponse(updatedSubscription, 'Subscription updated successfully');
}));

/**
 * DELETE /api/subscriptions/[id] - Cancel subscription by ID
 */
export const DELETE = requireStaff(withErrorHandling(async (request: AuthenticatedRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  // Get cancellation reason from query params
  const { searchParams } = new URL(request.url);
  const reason = searchParams.get('reason') || 'Cancelled by admin';

  // Find and cancel the subscription (don't actually delete, just mark as cancelled)
  const subscription = await Subscription.findOne({
    _id: validatedParams.id,
    gymId: gymId
  });
  
  if (!subscription) {
    return createErrorResponse('Subscription not found', 404);
  }

  // Use the instance method to cancel
  await subscription.cancel(reason);
  
  return createSuccessResponse(
    { 
      id: subscription._id,
      status: subscription.status,
      cancelledAt: new Date()
    },
    'Subscription cancelled successfully'
  );
}));
