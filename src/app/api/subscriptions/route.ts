import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Subscription from '@/models/Subscription';
import Member from '@/models/Member';
import Package from '@/models/Package';
import { subscriptionSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createPaginatedResponse,
  withErrorHandling,
  extractPaginationParams,
  buildSortObject,
  buildSearchFilter
} from '@/lib/utils/api';
import { requireStaff } from '@/lib/auth/middleware';
import { AuthenticatedRequest } from '@/lib/auth/middleware';

/**
 * GET /api/subscriptions - List subscriptions for current gym
 */
export const GET = requireStaff(withErrorHandling(async (request: AuthenticatedRequest) => {
  await connectToDatabase();
  
  const { searchParams } = new URL(request.url);
  const { page, limit, skip, search, sortBy, sortOrder } = extractPaginationParams(searchParams);

  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  // Create filter for gym isolation
  const tenantFilter = { gymId };
  
  // Build search filter for subscription-specific fields
  const searchFilter = buildSearchFilter(search, [
    'subscriptionNumber',
    'notes'
  ]);
  
  // Combine filters
  const filter: any = { ...tenantFilter, ...searchFilter };

  // Add status filter if provided
  const status = searchParams.get('status');
  if (status) {
    filter.status = status;
  }

  // Add member filter if provided
  const memberId = searchParams.get('memberId');
  if (memberId) {
    filter.memberId = memberId;
  }

  // Add package filter if provided
  const packageId = searchParams.get('packageId');
  if (packageId) {
    filter.packageId = packageId;
  }
  
  // Build sort object
  const sortObject = buildSortObject(sortBy, sortOrder);
  
  // Get total count for pagination
  const total = await Subscription.countDocuments(filter);
  
  // Get subscriptions with pagination and populate related data
  const subscriptions = await Subscription.find(filter)
    .sort(sortObject)
    .skip(skip)
    .limit(limit)
    .populate('memberId', 'personalInfo membershipId status')
    .populate('packageId', 'name type pricing duration')
    .select('-__v')
    .lean();
  
  return createPaginatedResponse(subscriptions, page, limit, total, 'Subscriptions retrieved successfully');
}));

/**
 * POST /api/subscriptions - Create a new subscription
 */
export const POST = requireStaff(withErrorHandling(async (request: AuthenticatedRequest) => {
  await connectToDatabase();
  
  const body = await request.json();

  // Get gymId from authenticated user context
  const gymId = request.user?.gymId;
  if (!gymId) {
    return createErrorResponse('Gym context not found', 400);
  }

  // Get createdBy from authenticated user
  const createdBy = request.user?.id;
  if (!createdBy) {
    return createErrorResponse('User context not found', 400);
  }

  // Validate request body
  const validatedData = subscriptionSchema.parse(body);

  // Verify member exists and belongs to this gym
  const member = await Member.findOne({
    _id: validatedData.memberId,
    gymId: gymId
  });
  
  if (!member) {
    return createErrorResponse('Member not found in this gym', 404);
  }

  // Verify package exists and belongs to this gym
  const packageData = await Package.findOne({
    _id: validatedData.packageId,
    gymId: gymId,
    isActive: true
  });
  
  if (!packageData) {
    return createErrorResponse('Package not found or inactive in this gym', 404);
  }

  // Calculate end date - use custom end date if provided, otherwise calculate from package duration
  const startDate = new Date(validatedData.startDate);
  let endDate: Date;

  if (validatedData.endDate) {
    endDate = new Date(validatedData.endDate);
  } else {
    endDate = new Date(startDate);
    switch (packageData.duration.unit) {
      case 'days':
        endDate.setDate(endDate.getDate() + packageData.duration.value);
        break;
      case 'weeks':
        endDate.setDate(endDate.getDate() + (packageData.duration.value * 7));
        break;
      case 'months':
        endDate.setMonth(endDate.getMonth() + packageData.duration.value);
        break;
      case 'years':
        endDate.setFullYear(endDate.getFullYear() + packageData.duration.value);
        break;
    }
  }

  // Handle flexible pricing - allow complete price override
  const originalAmount = packageData.pricing.amount;
  const customAmount = validatedData.payment.amount;
  const amount = customAmount !== undefined ? customAmount : originalAmount;
  const setupFee = validatedData.payment.setupFee || packageData.pricing.setupFee || 0;
  const discount = validatedData.payment.discount || 0;
  const finalAmount = Math.max(0, amount - discount);
  const totalPaid = finalAmount + setupFee;

  // Create new subscription with gym isolation and customizable fields
  const subscription = new Subscription({
    gymId,
    memberId: validatedData.memberId,
    packageId: validatedData.packageId,
    startDate: startDate,
    endDate: endDate,
    status: startDate <= new Date() ? 'active' : 'pending',
    payment: {
      amount: finalAmount,
      currency: validatedData.payment.currency || packageData.pricing.currency,
      setupFee: setupFee,
      totalPaid: totalPaid,
      paymentMethod: validatedData.payment.paymentMethod,
      paymentDate: new Date(),
      transactionId: validatedData.payment.transactionId,
      discount: discount,
      discountReason: validatedData.payment.discountReason,
      originalAmount: originalAmount,
    },
    autoRenewal: validatedData.autoRenewal || { enabled: false },
    usage: {
      classesAttended: 0,
      trainerSessions: 0,
    },
    notes: validatedData.notes,
    customTerms: validatedData.customTerms,
    createdBy: createdBy,
  });
  
  await subscription.save();

  // Populate the created subscription for response
  const populatedSubscription = await Subscription.findById(subscription._id)
    .populate('memberId', 'personalInfo membershipId')
    .populate('packageId', 'name type pricing duration')
    .select('-__v')
    .lean();
  
  return createSuccessResponse(
    populatedSubscription,
    'Subscription created successfully',
    201
  );
}));
