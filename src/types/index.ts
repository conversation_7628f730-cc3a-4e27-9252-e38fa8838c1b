// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'super_admin' | 'gym_admin' | 'gym_staff' | 'gym_owner';
  gymId?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'date' | 'checkbox';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

// Notification Types
export interface NotificationSettings {
  email: boolean;
  sms: boolean;
  whatsapp: boolean;
}

export interface WhatsAppConfig {
  enabled: boolean;
  accountSid?: string;
  authToken?: string;
  phoneNumber?: string;
}

// Common Types
export type Status = 'active' | 'inactive' | 'suspended' | 'cancelled';
export type PaymentMethod = 'cash' | 'card' | 'bank_transfer' | 'online' | 'other';
export type Gender = 'male' | 'female' | 'other';
export type EmploymentType = 'full-time' | 'part-time' | 'contract' | 'freelance';
export type SubscriptionStatus = 'active' | 'expired' | 'cancelled' | 'suspended' | 'pending';
export type PackageType = 'monthly' | 'quarterly' | 'yearly' | 'custom';
export type DurationUnit = 'days' | 'weeks' | 'months' | 'years';
