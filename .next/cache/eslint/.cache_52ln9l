[{"/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/[...nextauth]/route.ts": "1", "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/change-password/route.ts": "2", "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/forgot-password/route.ts": "3", "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/profile/route.ts": "4", "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/register/route.ts": "5", "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/reset-password/route.ts": "6", "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/verify-email/route.ts": "7", "/Users/<USER>/Documents/Projects/gymd/src/app/api/gyms/[id]/route.ts": "8", "/Users/<USER>/Documents/Projects/gymd/src/app/api/gyms/route.ts": "9", "/Users/<USER>/Documents/Projects/gymd/src/app/api/health/route.ts": "10", "/Users/<USER>/Documents/Projects/gymd/src/app/api/packages/[id]/route.ts": "11", "/Users/<USER>/Documents/Projects/gymd/src/app/api/packages/route.ts": "12", "/Users/<USER>/Documents/Projects/gymd/src/app/api/users/[id]/route.ts": "13", "/Users/<USER>/Documents/Projects/gymd/src/app/api/users/route.ts": "14", "/Users/<USER>/Documents/Projects/gymd/src/app/auth/error/page.tsx": "15", "/Users/<USER>/Documents/Projects/gymd/src/app/auth/signin/page.tsx": "16", "/Users/<USER>/Documents/Projects/gymd/src/app/auth/signup/page.tsx": "17", "/Users/<USER>/Documents/Projects/gymd/src/app/dashboard/page.tsx": "18", "/Users/<USER>/Documents/Projects/gymd/src/app/layout.tsx": "19", "/Users/<USER>/Documents/Projects/gymd/src/app/page.tsx": "20", "/Users/<USER>/Documents/Projects/gymd/src/components/landing/LandingPage.tsx": "21", "/Users/<USER>/Documents/Projects/gymd/src/components/providers/Providers.tsx": "22", "/Users/<USER>/Documents/Projects/gymd/src/components/ui/button.tsx": "23", "/Users/<USER>/Documents/Projects/gymd/src/components/ui/card.tsx": "24", "/Users/<USER>/Documents/Projects/gymd/src/components/ui/input.tsx": "25", "/Users/<USER>/Documents/Projects/gymd/src/components/ui/label.tsx": "26", "/Users/<USER>/Documents/Projects/gymd/src/lib/auth/config.ts": "27", "/Users/<USER>/Documents/Projects/gymd/src/lib/auth/middleware.ts": "28", "/Users/<USER>/Documents/Projects/gymd/src/lib/auth/utils.ts": "29", "/Users/<USER>/Documents/Projects/gymd/src/lib/database/connection.ts": "30", "/Users/<USER>/Documents/Projects/gymd/src/lib/utils/api.ts": "31", "/Users/<USER>/Documents/Projects/gymd/src/lib/utils.ts": "32", "/Users/<USER>/Documents/Projects/gymd/src/lib/validations/schemas.ts": "33", "/Users/<USER>/Documents/Projects/gymd/src/models/Gym.ts": "34", "/Users/<USER>/Documents/Projects/gymd/src/models/Member.ts": "35", "/Users/<USER>/Documents/Projects/gymd/src/models/Package.ts": "36", "/Users/<USER>/Documents/Projects/gymd/src/models/Subscription.ts": "37", "/Users/<USER>/Documents/Projects/gymd/src/models/Trainer.ts": "38", "/Users/<USER>/Documents/Projects/gymd/src/models/User.ts": "39", "/Users/<USER>/Documents/Projects/gymd/src/types/index.ts": "40"}, {"size": 168, "mtime": 1751196340086, "results": "41", "hashOfConfig": "42"}, {"size": 1485, "mtime": 1751196686372, "results": "43", "hashOfConfig": "42"}, {"size": 1301, "mtime": 1751196698679, "results": "44", "hashOfConfig": "42"}, {"size": 2606, "mtime": 1751196747936, "results": "45", "hashOfConfig": "42"}, {"size": 3530, "mtime": 1751196610211, "results": "46", "hashOfConfig": "42"}, {"size": 1200, "mtime": 1751196709500, "results": "47", "hashOfConfig": "42"}, {"size": 2218, "mtime": 1751196726463, "results": "48", "hashOfConfig": "42"}, {"size": 2674, "mtime": 1751223990810, "results": "49", "hashOfConfig": "42"}, {"size": 2709, "mtime": 1751223833350, "results": "50", "hashOfConfig": "42"}, {"size": 712, "mtime": 1751191430376, "results": "51", "hashOfConfig": "42"}, {"size": 3223, "mtime": 1751223798113, "results": "52", "hashOfConfig": "42"}, {"size": 2283, "mtime": 1751223780836, "results": "53", "hashOfConfig": "42"}, {"size": 6540, "mtime": 1751196582957, "results": "54", "hashOfConfig": "42"}, {"size": 4324, "mtime": 1751196571215, "results": "55", "hashOfConfig": "42"}, {"size": 4222, "mtime": 1751223644429, "results": "56", "hashOfConfig": "42"}, {"size": 4222, "mtime": 1751204193798, "results": "57", "hashOfConfig": "42"}, {"size": 3670, "mtime": 1751204653738, "results": "58", "hashOfConfig": "42"}, {"size": 8818, "mtime": 1751204057620, "results": "59", "hashOfConfig": "42"}, {"size": 524, "mtime": 1751204206947, "results": "60", "hashOfConfig": "42"}, {"size": 141, "mtime": 1751204255699, "results": "61", "hashOfConfig": "42"}, {"size": 9007, "mtime": 1751198298794, "results": "62", "hashOfConfig": "42"}, {"size": 264, "mtime": 1751203911527, "results": "63", "hashOfConfig": "42"}, {"size": 2035, "mtime": 1751198147844, "results": "64", "hashOfConfig": "42"}, {"size": 1906, "mtime": 1751198168382, "results": "65", "hashOfConfig": "42"}, {"size": 844, "mtime": 1751198178071, "results": "66", "hashOfConfig": "42"}, {"size": 594, "mtime": 1751198188006, "results": "67", "hashOfConfig": "42"}, {"size": 5216, "mtime": 1751223856435, "results": "68", "hashOfConfig": "42"}, {"size": 6760, "mtime": 1751197778968, "results": "69", "hashOfConfig": "42"}, {"size": 6843, "mtime": 1751196377387, "results": "70", "hashOfConfig": "42"}, {"size": 1930, "mtime": 1751197436726, "results": "71", "hashOfConfig": "42"}, {"size": 4185, "mtime": 1751196847311, "results": "72", "hashOfConfig": "42"}, {"size": 169, "mtime": 1751198154957, "results": "73", "hashOfConfig": "42"}, {"size": 11105, "mtime": 1751196489235, "results": "74", "hashOfConfig": "42"}, {"size": 4352, "mtime": 1751194852422, "results": "75", "hashOfConfig": "42"}, {"size": 5174, "mtime": 1751191345212, "results": "76", "hashOfConfig": "42"}, {"size": 4225, "mtime": 1751191313879, "results": "77", "hashOfConfig": "42"}, {"size": 6750, "mtime": 1751191414885, "results": "78", "hashOfConfig": "42"}, {"size": 7058, "mtime": 1751191382554, "results": "79", "hashOfConfig": "42"}, {"size": 7692, "mtime": 1751223682335, "results": "80", "hashOfConfig": "42"}, {"size": 1696, "mtime": 1751223656607, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jtrla8", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/change-password/route.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/forgot-password/route.ts", ["202", "203"], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/profile/route.ts", ["204"], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/register/route.ts", ["205", "206"], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/reset-password/route.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/verify-email/route.ts", ["207"], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/gyms/[id]/route.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/gyms/route.ts", ["208"], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/health/route.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/packages/[id]/route.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/packages/route.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/users/[id]/route.ts", ["209", "210", "211", "212"], [], "/Users/<USER>/Documents/Projects/gymd/src/app/api/users/route.ts", ["213", "214"], [], "/Users/<USER>/Documents/Projects/gymd/src/app/auth/error/page.tsx", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/auth/signin/page.tsx", ["215", "216"], [], "/Users/<USER>/Documents/Projects/gymd/src/app/auth/signup/page.tsx", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/dashboard/page.tsx", ["217", "218", "219", "220"], [], "/Users/<USER>/Documents/Projects/gymd/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/Projects/gymd/src/app/page.tsx", [], [], "/Users/<USER>/Documents/Projects/gymd/src/components/landing/LandingPage.tsx", ["221"], [], "/Users/<USER>/Documents/Projects/gymd/src/components/providers/Providers.tsx", [], [], "/Users/<USER>/Documents/Projects/gymd/src/components/ui/button.tsx", ["222"], [], "/Users/<USER>/Documents/Projects/gymd/src/components/ui/card.tsx", [], [], "/Users/<USER>/Documents/Projects/gymd/src/components/ui/input.tsx", ["223"], [], "/Users/<USER>/Documents/Projects/gymd/src/components/ui/label.tsx", [], [], "/Users/<USER>/Documents/Projects/gymd/src/lib/auth/config.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/lib/auth/middleware.ts", ["224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234", "235"], [], "/Users/<USER>/Documents/Projects/gymd/src/lib/auth/utils.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/lib/database/connection.ts", ["236"], [], "/Users/<USER>/Documents/Projects/gymd/src/lib/utils/api.ts", ["237", "238", "239", "240", "241", "242"], [], "/Users/<USER>/Documents/Projects/gymd/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/lib/validations/schemas.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/models/Gym.ts", [], [], "/Users/<USER>/Documents/Projects/gymd/src/models/Member.ts", ["243"], [], "/Users/<USER>/Documents/Projects/gymd/src/models/Package.ts", ["244"], [], "/Users/<USER>/Documents/Projects/gymd/src/models/Subscription.ts", ["245"], [], "/Users/<USER>/Documents/Projects/gymd/src/models/Trainer.ts", ["246", "247"], [], "/Users/<USER>/Documents/Projects/gymd/src/models/User.ts", ["248"], [], "/Users/<USER>/Documents/Projects/gymd/src/types/index.ts", ["249", "250"], [], {"ruleId": "251", "severity": 2, "message": "252", "line": 7, "column": 3, "nodeType": null, "messageId": "253", "endLine": 7, "endColumn": 22}, {"ruleId": "251", "severity": 2, "message": "254", "line": 36, "column": 9, "nodeType": null, "messageId": "253", "endLine": 36, "endColumn": 19}, {"ruleId": "251", "severity": 2, "message": "255", "line": 17, "column": 57, "nodeType": null, "messageId": "253", "endLine": 17, "endColumn": 64}, {"ruleId": "251", "severity": 2, "message": "256", "line": 68, "column": 9, "nodeType": null, "messageId": "253", "endLine": 68, "endColumn": 26}, {"ruleId": "251", "severity": 2, "message": "255", "line": 93, "column": 45, "nodeType": null, "messageId": "253", "endLine": 93, "endColumn": 52}, {"ruleId": "251", "severity": 2, "message": "256", "line": 76, "column": 9, "nodeType": null, "messageId": "253", "endLine": 76, "endColumn": 26}, {"ruleId": "251", "severity": 2, "message": "257", "line": 4, "column": 21, "nodeType": null, "messageId": "253", "endLine": 4, "endColumn": 37}, {"ruleId": "251", "severity": 2, "message": "258", "line": 117, "column": 13, "nodeType": null, "messageId": "253", "endLine": 117, "endColumn": 17}, {"ruleId": "251", "severity": 2, "message": "259", "line": 117, "column": 19, "nodeType": null, "messageId": "253", "endLine": 117, "endColumn": 25}, {"ruleId": "251", "severity": 2, "message": "260", "line": 117, "column": 27, "nodeType": null, "messageId": "253", "endLine": 117, "endColumn": 32}, {"ruleId": "251", "severity": 2, "message": "261", "line": 117, "column": 34, "nodeType": null, "messageId": "253", "endLine": 117, "endColumn": 40}, {"ruleId": "262", "severity": 2, "message": "263", "line": 38, "column": 16, "nodeType": "264", "messageId": "265", "endLine": 38, "endColumn": 19, "suggestions": "266"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 71, "column": 15, "nodeType": "264", "messageId": "265", "endLine": 71, "endColumn": 18, "suggestions": "267"}, {"ruleId": "251", "severity": 2, "message": "268", "line": 39, "column": 14, "nodeType": null, "messageId": "253", "endLine": 39, "endColumn": 17}, {"ruleId": "269", "severity": 2, "message": "270", "line": 127, "column": 18, "nodeType": "271", "messageId": "272", "suggestions": "273"}, {"ruleId": "251", "severity": 2, "message": "274", "line": 10, "column": 3, "nodeType": null, "messageId": "253", "endLine": 10, "endColumn": 13}, {"ruleId": "269", "severity": 2, "message": "270", "line": 114, "column": 17, "nodeType": "271", "messageId": "272", "suggestions": "275"}, {"ruleId": "269", "severity": 2, "message": "270", "line": 114, "column": 24, "nodeType": "271", "messageId": "272", "suggestions": "276"}, {"ruleId": "269", "severity": 2, "message": "270", "line": 150, "column": 71, "nodeType": "271", "messageId": "272", "suggestions": "277"}, {"ruleId": "251", "severity": 2, "message": "278", "line": 14, "column": 3, "nodeType": null, "messageId": "253", "endLine": 14, "endColumn": 7}, {"ruleId": "251", "severity": 2, "message": "279", "line": 44, "column": 32, "nodeType": null, "messageId": "253", "endLine": 44, "endColumn": 39}, {"ruleId": "280", "severity": 2, "message": "281", "line": 6, "column": 18, "nodeType": "282", "messageId": "283", "endLine": 6, "endColumn": 28, "suggestions": "284"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 45, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 45, "endColumn": 57, "suggestions": "285"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 48, "column": 49, "nodeType": "264", "messageId": "265", "endLine": 48, "endColumn": 52, "suggestions": "286"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 120, "column": 30, "nodeType": "264", "messageId": "265", "endLine": 120, "endColumn": 33, "suggestions": "287"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 151, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 151, "endColumn": 57, "suggestions": "288"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 158, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 158, "endColumn": 57, "suggestions": "289"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 168, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 168, "endColumn": 57, "suggestions": "290"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 178, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 178, "endColumn": 57, "suggestions": "291"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 188, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 188, "endColumn": 57, "suggestions": "292"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 198, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 198, "endColumn": 57, "suggestions": "293"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 208, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 208, "endColumn": 57, "suggestions": "294"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 218, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 218, "endColumn": 57, "suggestions": "295"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 229, "column": 54, "nodeType": "264", "messageId": "265", "endLine": 229, "endColumn": 57, "suggestions": "296"}, {"ruleId": "297", "severity": 2, "message": "298", "line": 12, "column": 5, "nodeType": "282", "messageId": "299", "endLine": 12, "endColumn": 28, "fix": "300"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 11, "column": 14, "nodeType": "264", "messageId": "265", "endLine": 11, "endColumn": 17, "suggestions": "301"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 31, "column": 13, "nodeType": "264", "messageId": "265", "endLine": 31, "endColumn": 16, "suggestions": "302"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 87, "column": 44, "nodeType": "264", "messageId": "265", "endLine": 87, "endColumn": 47, "suggestions": "303"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 98, "column": 68, "nodeType": "264", "messageId": "265", "endLine": 98, "endColumn": 71, "suggestions": "304"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 118, "column": 41, "nodeType": "264", "messageId": "265", "endLine": 118, "endColumn": 44, "suggestions": "305"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 120, "column": 45, "nodeType": "264", "messageId": "265", "endLine": 120, "endColumn": 48, "suggestions": "306"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 203, "column": 16, "nodeType": "264", "messageId": "265", "endLine": 203, "endColumn": 19, "suggestions": "307"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 170, "column": 16, "nodeType": "264", "messageId": "265", "endLine": 170, "endColumn": 19, "suggestions": "308"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 210, "column": 16, "nodeType": "264", "messageId": "265", "endLine": 210, "endColumn": 19, "suggestions": "309"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 261, "column": 16, "nodeType": "264", "messageId": "265", "endLine": 261, "endColumn": 19, "suggestions": "310"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 278, "column": 69, "nodeType": "264", "messageId": "265", "endLine": 278, "endColumn": 72, "suggestions": "311"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 294, "column": 18, "nodeType": "264", "messageId": "265", "endLine": 294, "endColumn": 21, "suggestions": "312"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 14, "column": 34, "nodeType": "264", "messageId": "265", "endLine": 14, "endColumn": 37, "suggestions": "313"}, {"ruleId": "262", "severity": 2, "message": "263", "line": 21, "column": 40, "nodeType": "264", "messageId": "265", "endLine": 21, "endColumn": 43, "suggestions": "314"}, "@typescript-eslint/no-unused-vars", "'createErrorResponse' is defined but never used.", "unusedVar", "'resetToken' is assigned a value but never used.", "'request' is defined but never used.", "'verificationToken' is assigned a value but never used.", "'paginationSchema' is defined but never used.", "'role' is assigned a value but never used.", "'status' is assigned a value but never used.", "'gymId' is assigned a value but never used.", "'gymIds' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["315", "316"], ["317", "318"], "'err' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["319", "320", "321", "322"], "'TrendingUp' is defined but never used.", ["323", "324", "325", "326"], ["327", "328", "329", "330"], ["331", "332", "333", "334"], "'Star' is defined but never used.", "'as<PERSON><PERSON>d' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["335"], ["336", "337"], ["338", "339"], ["340", "341"], ["342", "343"], ["344", "345"], ["346", "347"], ["348", "349"], ["350", "351"], ["352", "353"], ["354", "355"], ["356", "357"], ["358", "359"], "prefer-const", "'cached' is never reassigned. Use 'const' instead.", "useConst", {"range": "360", "text": "361"}, ["362", "363"], ["364", "365"], ["366", "367"], ["368", "369"], ["370", "371"], ["372", "373"], ["374", "375"], ["376", "377"], ["378", "379"], ["380", "381"], ["382", "383"], ["384", "385"], ["386", "387"], ["388", "389"], {"messageId": "390", "fix": "391", "desc": "392"}, {"messageId": "393", "fix": "394", "desc": "395"}, {"messageId": "390", "fix": "396", "desc": "392"}, {"messageId": "393", "fix": "397", "desc": "395"}, {"messageId": "398", "data": "399", "fix": "400", "desc": "401"}, {"messageId": "398", "data": "402", "fix": "403", "desc": "404"}, {"messageId": "398", "data": "405", "fix": "406", "desc": "407"}, {"messageId": "398", "data": "408", "fix": "409", "desc": "410"}, {"messageId": "398", "data": "411", "fix": "412", "desc": "401"}, {"messageId": "398", "data": "413", "fix": "414", "desc": "404"}, {"messageId": "398", "data": "415", "fix": "416", "desc": "407"}, {"messageId": "398", "data": "417", "fix": "418", "desc": "410"}, {"messageId": "398", "data": "419", "fix": "420", "desc": "401"}, {"messageId": "398", "data": "421", "fix": "422", "desc": "404"}, {"messageId": "398", "data": "423", "fix": "424", "desc": "407"}, {"messageId": "398", "data": "425", "fix": "426", "desc": "410"}, {"messageId": "398", "data": "427", "fix": "428", "desc": "401"}, {"messageId": "398", "data": "429", "fix": "430", "desc": "404"}, {"messageId": "398", "data": "431", "fix": "432", "desc": "407"}, {"messageId": "398", "data": "433", "fix": "434", "desc": "410"}, {"messageId": "435", "fix": "436", "desc": "437"}, {"messageId": "390", "fix": "438", "desc": "392"}, {"messageId": "393", "fix": "439", "desc": "395"}, {"messageId": "390", "fix": "440", "desc": "392"}, {"messageId": "393", "fix": "441", "desc": "395"}, {"messageId": "390", "fix": "442", "desc": "392"}, {"messageId": "393", "fix": "443", "desc": "395"}, {"messageId": "390", "fix": "444", "desc": "392"}, {"messageId": "393", "fix": "445", "desc": "395"}, {"messageId": "390", "fix": "446", "desc": "392"}, {"messageId": "393", "fix": "447", "desc": "395"}, {"messageId": "390", "fix": "448", "desc": "392"}, {"messageId": "393", "fix": "449", "desc": "395"}, {"messageId": "390", "fix": "450", "desc": "392"}, {"messageId": "393", "fix": "451", "desc": "395"}, {"messageId": "390", "fix": "452", "desc": "392"}, {"messageId": "393", "fix": "453", "desc": "395"}, {"messageId": "390", "fix": "454", "desc": "392"}, {"messageId": "393", "fix": "455", "desc": "395"}, {"messageId": "390", "fix": "456", "desc": "392"}, {"messageId": "393", "fix": "457", "desc": "395"}, {"messageId": "390", "fix": "458", "desc": "392"}, {"messageId": "393", "fix": "459", "desc": "395"}, {"messageId": "390", "fix": "460", "desc": "392"}, {"messageId": "393", "fix": "461", "desc": "395"}, [211, 300], "const cached: ConnectionCache = global.mongooseCache || {\n  conn: null,\n  promise: null,\n};", {"messageId": "390", "fix": "462", "desc": "392"}, {"messageId": "393", "fix": "463", "desc": "395"}, {"messageId": "390", "fix": "464", "desc": "392"}, {"messageId": "393", "fix": "465", "desc": "395"}, {"messageId": "390", "fix": "466", "desc": "392"}, {"messageId": "393", "fix": "467", "desc": "395"}, {"messageId": "390", "fix": "468", "desc": "392"}, {"messageId": "393", "fix": "469", "desc": "395"}, {"messageId": "390", "fix": "470", "desc": "392"}, {"messageId": "393", "fix": "471", "desc": "395"}, {"messageId": "390", "fix": "472", "desc": "392"}, {"messageId": "393", "fix": "473", "desc": "395"}, {"messageId": "390", "fix": "474", "desc": "392"}, {"messageId": "393", "fix": "475", "desc": "395"}, {"messageId": "390", "fix": "476", "desc": "392"}, {"messageId": "393", "fix": "477", "desc": "395"}, {"messageId": "390", "fix": "478", "desc": "392"}, {"messageId": "393", "fix": "479", "desc": "395"}, {"messageId": "390", "fix": "480", "desc": "392"}, {"messageId": "393", "fix": "481", "desc": "395"}, {"messageId": "390", "fix": "482", "desc": "392"}, {"messageId": "393", "fix": "483", "desc": "395"}, {"messageId": "390", "fix": "484", "desc": "392"}, {"messageId": "393", "fix": "485", "desc": "395"}, {"messageId": "390", "fix": "486", "desc": "392"}, {"messageId": "393", "fix": "487", "desc": "395"}, {"messageId": "390", "fix": "488", "desc": "392"}, {"messageId": "393", "fix": "489", "desc": "395"}, "suggestUnknown", {"range": "490", "text": "491"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "492", "text": "493"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "494", "text": "491"}, {"range": "495", "text": "493"}, "replaceWithAlt", {"alt": "496"}, {"range": "497", "text": "498"}, "Replace with `&apos;`.", {"alt": "499"}, {"range": "500", "text": "501"}, "Replace with `&lsquo;`.", {"alt": "502"}, {"range": "503", "text": "504"}, "Replace with `&#39;`.", {"alt": "505"}, {"range": "506", "text": "507"}, "Replace with `&rsquo;`.", {"alt": "496"}, {"range": "508", "text": "509"}, {"alt": "499"}, {"range": "510", "text": "511"}, {"alt": "502"}, {"range": "512", "text": "513"}, {"alt": "505"}, {"range": "514", "text": "515"}, {"alt": "496"}, {"range": "516", "text": "517"}, {"alt": "499"}, {"range": "518", "text": "519"}, {"alt": "502"}, {"range": "520", "text": "521"}, {"alt": "505"}, {"range": "522", "text": "523"}, {"alt": "496"}, {"range": "524", "text": "525"}, {"alt": "499"}, {"range": "526", "text": "527"}, {"alt": "502"}, {"range": "528", "text": "529"}, {"alt": "505"}, {"range": "530", "text": "531"}, "replaceEmptyInterfaceWithSuper", {"range": "532", "text": "533"}, "Replace empty interface with a type alias.", {"range": "534", "text": "491"}, {"range": "535", "text": "493"}, {"range": "536", "text": "491"}, {"range": "537", "text": "493"}, {"range": "538", "text": "491"}, {"range": "539", "text": "493"}, {"range": "540", "text": "491"}, {"range": "541", "text": "493"}, {"range": "542", "text": "491"}, {"range": "543", "text": "493"}, {"range": "544", "text": "491"}, {"range": "545", "text": "493"}, {"range": "546", "text": "491"}, {"range": "547", "text": "493"}, {"range": "548", "text": "491"}, {"range": "549", "text": "493"}, {"range": "550", "text": "491"}, {"range": "551", "text": "493"}, {"range": "552", "text": "491"}, {"range": "553", "text": "493"}, {"range": "554", "text": "491"}, {"range": "555", "text": "493"}, {"range": "556", "text": "491"}, {"range": "557", "text": "493"}, {"range": "558", "text": "491"}, {"range": "559", "text": "493"}, {"range": "560", "text": "491"}, {"range": "561", "text": "493"}, {"range": "562", "text": "491"}, {"range": "563", "text": "493"}, {"range": "564", "text": "491"}, {"range": "565", "text": "493"}, {"range": "566", "text": "491"}, {"range": "567", "text": "493"}, {"range": "568", "text": "491"}, {"range": "569", "text": "493"}, {"range": "570", "text": "491"}, {"range": "571", "text": "493"}, {"range": "572", "text": "491"}, {"range": "573", "text": "493"}, {"range": "574", "text": "491"}, {"range": "575", "text": "493"}, {"range": "576", "text": "491"}, {"range": "577", "text": "493"}, {"range": "578", "text": "491"}, {"range": "579", "text": "493"}, {"range": "580", "text": "491"}, {"range": "581", "text": "493"}, {"range": "582", "text": "491"}, {"range": "583", "text": "493"}, {"range": "584", "text": "491"}, {"range": "585", "text": "493"}, [1340, 1343], "unknown", [1340, 1343], "never", [2039, 2042], [2039, 2042], "&apos;", [3944, 3981], "\n              Don&apos;t have an account?", "&lsquo;", [3944, 3981], "\n              Don&lsquo;t have an account?", "&#39;", [3944, 3981], "\n              Don&#39;t have an account?", "&rsquo;", [3944, 3981], "\n              Don&rsquo;t have an account?", [3277, 3343], "\n            Here&apos;s what's happening at your gym today.\n          ", [3277, 3343], "\n            Here&lsquo;s what's happening at your gym today.\n          ", [3277, 3343], "\n            Here&#39;s what's happening at your gym today.\n          ", [3277, 3343], "\n            Here&rsquo;s what's happening at your gym today.\n          ", [3277, 3343], "\n            Here's what&apos;s happening at your gym today.\n          ", [3277, 3343], "\n            Here's what&lsquo;s happening at your gym today.\n          ", [3277, 3343], "\n            Here's what&#39;s happening at your gym today.\n          ", [3277, 3343], "\n            Here's what&rsquo;s happening at your gym today.\n          ", [4894, 4911], "Today&apos;s Check-ins", [4894, 4911], "Today&lsquo;s Check-ins", [4894, 4911], "Today&#39;s Check-ins", [4894, 4911], "Today&rsquo;s Check-ins", [89, 166], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [1087, 1090], [1087, 1090], [1208, 1211], [1208, 1211], [3441, 3444], [3441, 3444], [4454, 4457], [4454, 4457], [4653, 4656], [4653, 4656], [4933, 4936], [4933, 4936], [5233, 5236], [5233, 5236], [5469, 5472], [5469, 5472], [5727, 5730], [5727, 5730], [5985, 5988], [5985, 5988], [6227, 6230], [6227, 6230], [6544, 6547], [6544, 6547], [258, 261], [258, 261], [576, 579], [576, 579], [1599, 1602], [1599, 1602], [2024, 2027], [2024, 2027], [2535, 2538], [2535, 2538], [2613, 2616], [2613, 2616], [4795, 4798], [4795, 4798], [3392, 3395], [3392, 3395], [5056, 5059], [5056, 5059], [6349, 6352], [6349, 6352], [6811, 6814], [6811, 6814], [7124, 7127], [7124, 7127], [285, 288], [285, 288], [404, 407], [404, 407]]