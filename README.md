# GymD - Gym Management System

A comprehensive gym management solution built with Next.js 14, MongoDB, and modern architecture to handle gym operations efficiently.

## 🏋️ Features

- **Gym Management**: Comprehensive gym management solution
- **Gym Package Management**: Create and manage membership packages with flexible pricing
- **Trainer Management**: Manage gym trainers, schedules, and certifications
- **Member Management**: Handle gym members, subscriptions, and profiles
- **Flexible Subscriptions**: Customizable subscription system with various start dates
- **WhatsApp Integration**: Automated notifications via WhatsApp Business API
- **Role-Based Access Control**: Different access levels for gym owners, admins, and staff
- **Responsive Design**: Modern UI built with Tailwind CSS and Radix UI components

## 🚀 Tech Stack

- **Frontend**: Next.js 14 (App Router), React 19, TypeScript
- **Styling**: Tailwind CSS, Radix UI Components
- **Backend**: Next.js API Routes, MongoDB with Mongoose
- **Authentication**: NextAuth.js
- **Validation**: Zod schemas with React Hook Form
- **Notifications**: Twilio WhatsApp Business API
- **Icons**: Lucide React

## 🏗️ Architecture

### Architecture Design
- **Data Management**: Efficient database queries with gymId filtering
- **Authentication**: Secure user authentication and session management
- **Scalability**: Designed to handle gym operations efficiently

### Database Schema
- **Gyms**: Tenant configuration and settings
- **Packages**: Membership packages with pricing and restrictions
- **Members**: Customer profiles and preferences
- **Trainers**: Staff management with certifications
- **Subscriptions**: Flexible subscription system with payment tracking

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gymd
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Configure the following variables:
   ```env
   # Database
   MONGODB_URI=mongodb://localhost:27017/gymd

   # Authentication
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key
   JWT_SECRET=your-jwt-secret

   # WhatsApp/Twilio
   TWILIO_ACCOUNT_SID=your-twilio-sid
   TWILIO_AUTH_TOKEN=your-twilio-token
   TWILIO_WHATSAPP_FROM=whatsapp:+***********

   # App Configuration
   APP_URL=http://localhost:3000
   ```

4. **Start MongoDB**
   ```bash
   # Using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:latest

   # Or install MongoDB locally
   brew install mongodb/brew/mongodb-community
   brew services start mongodb/brew/mongodb-community
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   - Main site: [http://localhost:3000](http://localhost:3000)
   - Gym1 tenant: [http://gym1.localhost:3000](http://gym1.localhost:3000)
   - Gym2 tenant: [http://gym2.localhost:3000](http://gym2.localhost:3000)

## 📁 Project Structure

```
gymd/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes
│   │   │   └── health/        # Health check endpoint
│   │   ├── auth/              # Authentication pages
│   │   └── dashboard/         # Dashboard pages
│   ├── components/            # React components
│   │   ├── landing/           # Landing page components
│   │   ├── providers/         # Context providers
│   │   └── ui/               # Reusable UI components
│   ├── lib/                   # Utility libraries
│   │   ├── auth/             # Authentication utilities
│   │   ├── database/         # Database connection
│   │   ├── utils/            # General utilities
│   │   └── validations/      # Zod schemas
│   ├── models/               # MongoDB models
│   │   ├── Gym.ts
│   │   ├── Package.ts
│   │   ├── Member.ts
│   │   ├── Trainer.ts
│   │   └── Subscription.ts
│   └── types/                # TypeScript type definitions
├── public/                   # Static assets
└── package.json
```

## 🔧 API Endpoints

### Health Check
- `GET /api/health` - System health status

### Gym Management (Coming Soon)
- `GET /api/gyms` - List gyms (super admin only)
- `POST /api/gyms` - Create new gym
- `GET /api/gyms/[id]` - Get gym details
- `PUT /api/gyms/[id]` - Update gym
- `DELETE /api/gyms/[id]` - Delete gym

## 🧪 Testing

The application includes comprehensive testing setup:

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your repository to Vercel
2. Configure environment variables
3. Deploy automatically on push

### Docker
```bash
# Build the image
docker build -t gymd .

# Run the container
docker run -p 3000:3000 gymd
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [docs.gymd.com](https://docs.gymd.com)
- Issues: [GitHub Issues](https://github.com/your-repo/gymd/issues)
